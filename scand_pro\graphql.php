<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);



// Enable CORS - Replace YOUR_VERCEL_URL with your actual Vercel domain
$allowed_origins = [
    'https://YOUR_VERCEL_URL.vercel.app',
    'http://localhost:3000',
    'http://localhost:3001'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: $origin");
}

header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header("Access-Control-Allow-Credentials: true");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/Controller/GraphQL.php';

use App\Controller\GraphQL;

error_log("GraphQL endpoint called at " . date('Y-m-d H:i:s'));
echo GraphQL::handle();
